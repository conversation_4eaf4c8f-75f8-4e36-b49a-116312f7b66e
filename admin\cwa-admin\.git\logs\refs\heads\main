0000000000000000000000000000000000000000 572be274770582ed9734926c9a75136a416d9fc2 Anas-<PERSON>-3673 <<EMAIL>> 1750990051 +0500	commit (initial): first commit
572be274770582ed9734926c9a75136a416d9fc2 572be274770582ed9734926c9a75136a416d9fc2 Anas-<PERSON>-3673 <<EMAIL>> 1750990052 +0500	Branch: renamed refs/heads/master to refs/heads/main
572be274770582ed9734926c9a75136a416d9fc2 9933866833b463bc772b6c724577097643f19e1c Anas-Ali-3673 <<EMAIL>> 1750990092 +0500	commit: first commit
9933866833b463bc772b6c724577097643f19e1c 47bc61de950b118309701cff0f41ade9729e8c3f Anas-Ali-3673 <<EMAIL>> 1750991789 +0500	commit: Refactor API routes and services to handle DELETE requests with improved error handling and authorization
47bc61de950b118309701cff0f41ade9729e8c3f 8e4eb0a6126e205149197f40004168f003b4ca59 Anas-Ali-3673 <<EMAIL>> 1750993030 +0500	commit: Enhance mobile responsiveness and UI consistency across login, dashboard, and layout components
8e4eb0a6126e205149197f40004168f003b4ca59 b2ac94daef4fcb13b76dcf34fade0afeb8a63f62 Anas-Ali-3673 <<EMAIL>> 1751942257 +0500	commit: add product
b2ac94daef4fcb13b76dcf34fade0afeb8a63f62 df12c2eb10850a2e60f804f8c34ca1464aa52980 Anas-Ali-3673 <<EMAIL>> 1752041097 +0500	commit: blogs
