/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/video-blogs/[id]/edit/page";
exports.ids = ["app/video-blogs/[id]/edit/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&page=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&appPaths=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&pagePath=private-next-app-dir%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&page=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&appPaths=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&pagePath=private-next-app-dir%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/video-blogs/[id]/edit/page.tsx */ \"(rsc)/./src/app/video-blogs/[id]/edit/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'video-blogs',\n        {\n        children: [\n        '[id]',\n        {\n        children: [\n        'edit',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/video-blogs/[id]/edit/page\",\n        pathname: \"/video-blogs/[id]/edit\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&page=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&appPaths=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&pagePath=private-next-app-dir%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(rsc)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cvideo-blogs%5C%5C%5Bid%5D%5C%5Cedit%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cvideo-blogs%5C%5C%5Bid%5D%5C%5Cedit%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/video-blogs/[id]/edit/page.tsx */ \"(rsc)/./src/app/video-blogs/[id]/edit/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3ZpZGVvLWJsb2dzJTVDJTVDJTVCaWQlNUQlNUMlNUNlZGl0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFxSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxzcmNcXFxcYXBwXFxcXHZpZGVvLWJsb2dzXFxcXFtpZF1cXFxcZWRpdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cvideo-blogs%5C%5C%5Bid%5D%5C%5Cedit%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQUxJIENPTVBVVEVSU1xcRGVza3RvcFxcYWRtaW5cXGN3YS1hZG1pblxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a96de9275224\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFMSSBDT01QVVRFUlNcXERlc2t0b3BcXGFkbWluXFxjd2EtYWRtaW5cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE5NmRlOTI3NTIyNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/Providers */ \"(rsc)/./src/components/providers/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"CWA Admin Dashboard\",\n    description: \"Admin dashboard for CWA application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSaUI7QUFDa0M7QUFZbEQsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHWCwyTEFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdNQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFcEUsNEVBQUNDLHVFQUFTQTswQkFDUEs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCBQcm92aWRlcnMgZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNXQSBBZG1pbiBEYXNoYm9hcmRcIixcbiAgZGVzY3JpcHRpb246IFwiQWRtaW4gZGFzaGJvYXJkIGZvciBDV0EgYXBwbGljYXRpb25cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJQcm92aWRlcnMiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/video-blogs/[id]/edit/page.tsx":
/*!************************************************!*\
  !*** ./src/app/video-blogs/[id]/edit/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\app\\video-blogs\\[id]\\edit\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\admin\\cwa-admin\\src\\components\\providers\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./lib/api/apiClient.ts":
/*!******************************!*\
  !*** ./lib/api/apiClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// API base URL - using the proxied URL to avoid CORS issues\nconst API_BASE_URL = '/api';\nconsole.log('API Base URL (proxied):', API_BASE_URL);\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    // Set withCredentials to true since the backend is configured with credentials\n    withCredentials: true,\n    // Add a timeout to prevent hanging requests\n    timeout: 15000\n});\n// Request interceptor to add auth token if available\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage if available\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    // Log successful responses for debugging\n    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);\n    return response;\n}, (error)=>{\n    // Handle common errors here\n    if (error.response) {\n        // Server responded with an error status\n        console.error('API Error:', error.response.status, error.response.data);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Handle 401 Unauthorized - could redirect to login\n        if (error.response.status === 401) {\n            // Don't automatically log out for password change errors\n            if (error.config.url?.includes('/password')) {\n                console.log('Password change authentication error - not logging out user');\n            } else {\n                // Clear auth data and redirect to login if needed\n                if (false) {}\n            }\n        }\n        // Handle 403 Forbidden\n        if (error.response.status === 403) {\n            console.error('Forbidden access:', error.response.data);\n        }\n        // Handle 404 Not Found\n        if (error.response.status === 404) {\n            console.error('Resource not found:', error.response.data);\n        }\n        // Handle 500 Server Error\n        if (error.response.status >= 500) {\n            console.error('Server error:', error.response.data);\n        }\n    } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error - No response received:', error.request);\n        console.error('Request URL:', error.config.url);\n        console.error('Request Method:', error.config.method);\n        console.error('Request Data:', error.config.data);\n        // Check if it's a timeout\n        if (error.code === 'ECONNABORTED') {\n            console.error('Request timeout. Please check your internet connection.');\n        }\n    } else {\n        // Something else happened\n        console.error('Error:', error.message);\n        if (error.config) {\n            console.error('Request URL:', error.config.url);\n            console.error('Request Method:', error.config.method);\n            console.error('Request Data:', error.config.data);\n        }\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/Providers.tsx */ \"(ssr)/./src/components/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cvideo-blogs%5C%5C%5Bid%5D%5C%5Cedit%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cvideo-blogs%5C%5C%5Bid%5D%5C%5Cedit%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/video-blogs/[id]/edit/page.tsx */ \"(ssr)/./src/app/video-blogs/[id]/edit/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FMSSUyMENPTVBVVEVSUyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZG1pbiU1QyU1Q2N3YS1hZG1pbiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3ZpZGVvLWJsb2dzJTVDJTVDJTVCaWQlNUQlNUMlNUNlZGl0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFxSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQUxJIENPTVBVVEVSU1xcXFxEZXNrdG9wXFxcXGFkbWluXFxcXGN3YS1hZG1pblxcXFxzcmNcXFxcYXBwXFxcXHZpZGVvLWJsb2dzXFxcXFtpZF1cXFxcZWRpdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CALI%20COMPUTERS%5C%5CDesktop%5C%5Cadmin%5C%5Ccwa-admin%5C%5Csrc%5C%5Capp%5C%5Cvideo-blogs%5C%5C%5Bid%5D%5C%5Cedit%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/video-blogs/[id]/edit/page.tsx":
/*!************************************************!*\
  !*** ./src/app/video-blogs/[id]/edit/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditVideoBlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_video_blogs_VideoBlogForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/video-blogs/VideoBlogForm */ \"(ssr)/./src/components/video-blogs/VideoBlogForm.tsx\");\n/* harmony import */ var _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/videoBlogService */ \"(ssr)/./src/lib/api/videoBlogService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction EditVideoBlogPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [videoBlog, setVideoBlog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fetchLoading, setFetchLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const videoBlogId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditVideoBlogPage.useEffect\": ()=>{\n            const fetchVideoBlog = {\n                \"EditVideoBlogPage.useEffect.fetchVideoBlog\": async ()=>{\n                    try {\n                        setFetchLoading(true);\n                        setError(null);\n                        const response = await _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getVideoBlog(videoBlogId);\n                        if (response.status === 'success') {\n                            setVideoBlog(response.data.videoBlog);\n                        } else {\n                            throw new Error(response.message || 'Failed to fetch video blog');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching video blog:', error);\n                        setError(error.message || 'Failed to fetch video blog');\n                    } finally{\n                        setFetchLoading(false);\n                    }\n                }\n            }[\"EditVideoBlogPage.useEffect.fetchVideoBlog\"];\n            if (videoBlogId) {\n                fetchVideoBlog();\n            }\n        }\n    }[\"EditVideoBlogPage.useEffect\"], [\n        videoBlogId\n    ]);\n    const handleSubmit = async (data)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].updateVideoBlog(videoBlogId, data);\n            if (response.status === 'success') {\n                // Show success message\n                alert('Video blog updated successfully!');\n                // Redirect to video blogs list\n                router.push('/video-blogs');\n            } else {\n                throw new Error(response.message || 'Failed to update video blog');\n            }\n        } catch (error) {\n            console.error('Error updating video blog:', error);\n            alert('Failed to update video blog: ' + (error.message || 'Unknown error'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        router.push('/video-blogs');\n    };\n    if (fetchLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 p-4 md:p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-2/3 mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 bg-gray-200 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-24 bg-gray-200 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 bg-gray-200 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 p-4 md:p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-red-800\",\n                                        children: \"Error\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-sm text-red-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.reload(),\n                                                className: \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                children: \"Try Again\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancel,\n                                                className: \"bg-white px-3 py-2 rounded-md text-sm font-medium text-gray-700 border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: \"Go Back\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen w-full max-w-full overflow-x-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 p-4 md:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-4 md:p-6 rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-w-0 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl md:text-2xl font-bold text-gray-900 break-words\",\n                                        children: \"Edit Video Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1 text-sm md:text-base break-words\",\n                                        children: \"Update the video blog information and settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    videoBlog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs md:text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            \"Editing: \",\n                                            videoBlog.title\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    videoBlog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_video_blogs_VideoBlogForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        videoBlog: videoBlog,\n                        onSubmit: handleSubmit,\n                        onCancel: handleCancel,\n                        loading: loading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\video-blogs\\\\[id]\\\\edit\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/video-blogs/[id]/edit/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children }) {\n    const { isAuthenticated, isLoading } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                router.push('/auth/login');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, don't render children (redirect will happen)\n    if (!isAuthenticated) {\n        return null;\n    }\n    // If authenticated, render the protected content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50\",\n            style: {\n                backgroundColor: 'var(--background)'\n            },\n            children: [\n                sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden transition-opacity duration-300\",\n                    onClick: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onClose: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col lg:ml-64 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onMenuClick: ()=>setSidebarOpen(true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-3 sm:p-4 md:p-6 overflow-y-auto bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-full xl:max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\DashboardLayout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,BellIcon,ChevronDownIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header({ onMenuClick }) {\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    const getPageTitle = ()=>{\n        switch(pathname){\n            case '/dashboard':\n                return 'Dashboard';\n            case '/users':\n                return 'User Management';\n            case '/products':\n                return 'Product Management';\n            case '/video-blogs':\n                return 'Video Blog Management';\n            case '/video-blogs/add':\n                return 'Add Video Blog';\n            case '/orders':\n                return 'Order Management';\n            case '/settings':\n                return 'Settings';\n            default:\n                if (pathname.startsWith('/video-blogs/') && pathname.includes('/edit')) {\n                    return 'Edit Video Blog';\n                }\n                return 'CWA Admin Dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-14 sm:h-16 flex items-center px-3 sm:px-4 md:px-6 border-b\",\n        style: {\n            backgroundColor: 'var(--header-background)',\n            boxShadow: 'var(--header-shadow)',\n            borderColor: 'var(--border-color)'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onMenuClick,\n                className: \"lg:hidden p-1.5 sm:p-2 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100 transition-colors mr-2 sm:mr-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base sm:text-lg md:text-xl font-semibold truncate\",\n                        style: {\n                            color: 'var(--foreground)'\n                        },\n                        children: getPageTitle()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs sm:text-sm text-slate-500 mt-0.5 hidden sm:block\",\n                        children: \"Welcome back to your admin dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"relative p-1.5 sm:p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute top-0.5 sm:top-1 right-0.5 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-3 text-slate-700 hover:text-slate-900 focus:outline-none p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs sm:text-sm font-medium\",\n                                            children: user?.name || 'Admin User'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500 hidden md:block\",\n                                            children: \"Administrator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm\",\n                                        children: user?.name ? getInitials(user.name) : 'AU'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_BellIcon_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-3 h-3 sm:w-4 sm:h-4 hidden sm:block\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDZ0Q7QUFDRjtBQUtUO0FBTXRCLFNBQVNLLE9BQU8sRUFBRUMsV0FBVyxFQUFlO0lBQ3pELE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdQLDZEQUFPQTtJQUN4QixNQUFNUSxXQUFXUCw0REFBV0E7SUFFNUIsTUFBTVEsY0FBYyxDQUFDQztRQUNuQixPQUFPQSxLQUNKQyxLQUFLLENBQUMsS0FDTkMsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxNQUFNLENBQUMsSUFDeEJDLElBQUksQ0FBQyxJQUNMQyxXQUFXLEdBQ1hDLEtBQUssQ0FBQyxHQUFHO0lBQ2Q7SUFFQSxNQUFNQyxlQUFlO1FBQ25CLE9BQVFWO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsSUFBSUEsU0FBU1csVUFBVSxDQUFDLG9CQUFvQlgsU0FBU1ksUUFBUSxDQUFDLFVBQVU7b0JBQ3RFLE9BQU87Z0JBQ1Q7Z0JBQ0EsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ0MsV0FBVTtRQUNWQyxPQUFPO1lBQ0xDLGlCQUFpQjtZQUNqQkMsV0FBVztZQUNYQyxhQUFhO1FBQ2Y7OzBCQUdBLDhEQUFDQztnQkFDQ0MsU0FBU3RCO2dCQUNUZ0IsV0FBVTswQkFFViw0RUFBQ2xCLDRIQUFTQTtvQkFBQ2tCLFdBQVU7Ozs7Ozs7Ozs7OzBCQUd2Qiw4REFBQ087Z0JBQUlQLFdBQVU7O2tDQUNiLDhEQUFDUTt3QkFBR1IsV0FBVTt3QkFBeURDLE9BQU87NEJBQUVRLE9BQU87d0JBQW9CO2tDQUN4R2I7Ozs7OztrQ0FFSCw4REFBQ2M7d0JBQUVWLFdBQVU7a0NBQTJEOzs7Ozs7Ozs7Ozs7MEJBSzFFLDhEQUFDTztnQkFBSVAsV0FBVTs7a0NBRWIsOERBQUNLO3dCQUFPTCxXQUFVOzswQ0FDaEIsOERBQUNwQiw0SEFBUUE7Z0NBQUNvQixXQUFVOzs7Ozs7MENBQ3BCLDhEQUFDVztnQ0FBS1gsV0FBVTs7Ozs7Ozs7Ozs7O2tDQUlsQiw4REFBQ087d0JBQUlQLFdBQVU7a0NBQ2IsNEVBQUNLOzRCQUFPTCxXQUFVOzs4Q0FDaEIsOERBQUNPO29DQUFJUCxXQUFVOztzREFDYiw4REFBQ1U7NENBQUVWLFdBQVU7c0RBQWtDZixNQUFNRyxRQUFROzs7Ozs7c0RBQzdELDhEQUFDc0I7NENBQUVWLFdBQVU7c0RBQXlDOzs7Ozs7Ozs7Ozs7OENBRXhELDhEQUFDTztvQ0FBSVAsV0FBVTs4Q0FDYiw0RUFBQ1c7d0NBQUtYLFdBQVU7a0RBQ2JmLE1BQU1HLE9BQU9ELFlBQVlGLEtBQUtHLElBQUksSUFBSTs7Ozs7Ozs7Ozs7OENBRzNDLDhEQUFDUCw0SEFBZUE7b0NBQUNtQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU12QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcSGVhZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0L0F1dGhDb250ZXh0JztcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7XG4gIEJlbGxJY29uLFxuICBDaGV2cm9uRG93bkljb24sXG4gIEJhcnMzSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG5pbnRlcmZhY2UgSGVhZGVyUHJvcHMge1xuICBvbk1lbnVDbGljazogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVhZGVyKHsgb25NZW51Q2xpY2sgfTogSGVhZGVyUHJvcHMpIHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcblxuICBjb25zdCBnZXRJbml0aWFscyA9IChuYW1lOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmFtZVxuICAgICAgLnNwbGl0KCcgJylcbiAgICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKSlcbiAgICAgIC5qb2luKCcnKVxuICAgICAgLnRvVXBwZXJDYXNlKClcbiAgICAgIC5zbGljZSgwLCAyKTtcbiAgfTtcblxuICBjb25zdCBnZXRQYWdlVGl0bGUgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChwYXRobmFtZSkge1xuICAgICAgY2FzZSAnL2Rhc2hib2FyZCc6XG4gICAgICAgIHJldHVybiAnRGFzaGJvYXJkJztcbiAgICAgIGNhc2UgJy91c2Vycyc6XG4gICAgICAgIHJldHVybiAnVXNlciBNYW5hZ2VtZW50JztcbiAgICAgIGNhc2UgJy9wcm9kdWN0cyc6XG4gICAgICAgIHJldHVybiAnUHJvZHVjdCBNYW5hZ2VtZW50JztcbiAgICAgIGNhc2UgJy92aWRlby1ibG9ncyc6XG4gICAgICAgIHJldHVybiAnVmlkZW8gQmxvZyBNYW5hZ2VtZW50JztcbiAgICAgIGNhc2UgJy92aWRlby1ibG9ncy9hZGQnOlxuICAgICAgICByZXR1cm4gJ0FkZCBWaWRlbyBCbG9nJztcbiAgICAgIGNhc2UgJy9vcmRlcnMnOlxuICAgICAgICByZXR1cm4gJ09yZGVyIE1hbmFnZW1lbnQnO1xuICAgICAgY2FzZSAnL3NldHRpbmdzJzpcbiAgICAgICAgcmV0dXJuICdTZXR0aW5ncyc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL3ZpZGVvLWJsb2dzLycpICYmIHBhdGhuYW1lLmluY2x1ZGVzKCcvZWRpdCcpKSB7XG4gICAgICAgICAgcmV0dXJuICdFZGl0IFZpZGVvIEJsb2cnO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAnQ1dBIEFkbWluIERhc2hib2FyZCc7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlclxuICAgICAgY2xhc3NOYW1lPVwiaC0xNCBzbTpoLTE2IGZsZXggaXRlbXMtY2VudGVyIHB4LTMgc206cHgtNCBtZDpweC02IGJvcmRlci1iXCJcbiAgICAgIHN0eWxlPXt7XG4gICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3ZhcigtLWhlYWRlci1iYWNrZ3JvdW5kKScsXG4gICAgICAgIGJveFNoYWRvdzogJ3ZhcigtLWhlYWRlci1zaGFkb3cpJyxcbiAgICAgICAgYm9yZGVyQ29sb3I6ICd2YXIoLS1ib3JkZXItY29sb3IpJ1xuICAgICAgfX1cbiAgICA+XG4gICAgICB7LyogTW9iaWxlIE1lbnUgQnV0dG9uICovfVxuICAgICAgPGJ1dHRvblxuICAgICAgICBvbkNsaWNrPXtvbk1lbnVDbGlja31cbiAgICAgICAgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHAtMS41IHNtOnAtMiByb3VuZGVkLWxnIHRleHQtc2xhdGUtNjAwIGhvdmVyOnRleHQtc2xhdGUtODAwIGhvdmVyOmJnLXNsYXRlLTEwMCB0cmFuc2l0aW9uLWNvbG9ycyBtci0yIHNtOm1yLTNcIlxuICAgICAgPlxuICAgICAgICA8QmFyczNJY29uIGNsYXNzTmFtZT1cInctNSBoLTUgc206dy02IHNtOmgtNlwiIC8+XG4gICAgICA8L2J1dHRvbj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHNtOnRleHQtbGcgbWQ6dGV4dC14bCBmb250LXNlbWlib2xkIHRydW5jYXRlXCIgc3R5bGU9e3sgY29sb3I6ICd2YXIoLS1mb3JlZ3JvdW5kKScgfX0+XG4gICAgICAgICAge2dldFBhZ2VUaXRsZSgpfVxuICAgICAgICA8L2gyPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHNtOnRleHQtc20gdGV4dC1zbGF0ZS01MDAgbXQtMC41IGhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgIFdlbGNvbWUgYmFjayB0byB5b3VyIGFkbWluIGRhc2hib2FyZFxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgc206c3BhY2UteC0yIG1kOnNwYWNlLXgtNFwiPlxuICAgICAgICB7LyogTm90aWZpY2F0aW9ucyAqL31cbiAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTEuNSBzbTpwLTIgdGV4dC1zbGF0ZS02MDAgaG92ZXI6dGV4dC1zbGF0ZS04MDAgaG92ZXI6Ymctc2xhdGUtMTAwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICA8QmVsbEljb24gY2xhc3NOYW1lPVwidy00IGgtNCBzbTp3LTUgc206aC01XCIgLz5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMC41IHNtOnRvcC0xIHJpZ2h0LTAuNSBzbTpyaWdodC0xIHctMS41IGgtMS41IHNtOnctMiBzbTpoLTIgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgIHsvKiBVc2VyIFByb2ZpbGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBzbTpzcGFjZS14LTIgbWQ6c3BhY2UteC0zIHRleHQtc2xhdGUtNzAwIGhvdmVyOnRleHQtc2xhdGUtOTAwIGZvY3VzOm91dGxpbmUtbm9uZSBwLTEuNSBzbTpwLTIgcm91bmRlZC1sZyBob3ZlcjpiZy1zbGF0ZS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBoaWRkZW4gc206YmxvY2tcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e3VzZXI/Lm5hbWUgfHwgJ0FkbWluIFVzZXInfTwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTUwMCBoaWRkZW4gbWQ6YmxvY2tcIj5BZG1pbmlzdHJhdG9yPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNyBoLTcgc206dy04IHNtOmgtOCBtZDp3LTEwIG1kOmgtMTAgcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1ibHVlLTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIHt1c2VyPy5uYW1lID8gZ2V0SW5pdGlhbHModXNlci5uYW1lKSA6ICdBVSd9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zIHNtOnctNCBzbTpoLTQgaGlkZGVuIHNtOmJsb2NrXCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VBdXRoIiwidXNlUGF0aG5hbWUiLCJCZWxsSWNvbiIsIkNoZXZyb25Eb3duSWNvbiIsIkJhcnMzSWNvbiIsIkhlYWRlciIsIm9uTWVudUNsaWNrIiwidXNlciIsInBhdGhuYW1lIiwiZ2V0SW5pdGlhbHMiLCJuYW1lIiwic3BsaXQiLCJtYXAiLCJ3b3JkIiwiY2hhckF0Iiwiam9pbiIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJnZXRQYWdlVGl0bGUiLCJzdGFydHNXaXRoIiwiaW5jbHVkZXMiLCJoZWFkZXIiLCJjbGFzc05hbWUiLCJzdHlsZSIsImJhY2tncm91bmRDb2xvciIsImJveFNoYWRvdyIsImJvcmRlckNvbG9yIiwiYnV0dG9uIiwib25DbGljayIsImRpdiIsImgyIiwiY29sb3IiLCJwIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ClipboardDocumentListIcon,CogIcon,HomeIcon,ShoppingBagIcon,UsersIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ isOpen, onClose }) {\n    const { logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const handleLogout = ()=>{\n        logout();\n    };\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: '/users',\n            label: 'Users',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: '/products',\n            label: 'Products',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: '/video-blogs',\n            label: 'Video Blogs',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: '/orders',\n            label: 'Orders',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            href: '/settings',\n            label: 'Settings',\n            icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        }\n    ];\n    const handleNavClick = ()=>{\n        // Close sidebar on mobile when navigation item is clicked\n        if (window.innerWidth < 1024) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `\n        w-64 h-screen fixed left-0 top-0 z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : '-translate-x-full'}\n        lg:translate-x-0 bg-slate-800 shadow-xl\n      `,\n        style: {\n            backgroundColor: 'var(--sidebar-background)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 lg:p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6 lg:mb-8 lg:block\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl lg:text-2xl font-bold text-white\",\n                                    children: \"CWA Admin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs lg:text-sm text-slate-300 mt-1\",\n                                    children: \"Management Portal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"lg:hidden p-2 rounded-lg text-slate-300 hover:bg-slate-700 hover:text-white transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1 lg:space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    onClick: handleNavClick,\n                                    className: `flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg transition-all duration-200 ${isActive ? 'bg-blue-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700 hover:text-white'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm lg:text-base\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 lg:mt-8 pt-4 lg:pt-6 border-t border-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"flex items-center gap-3 px-3 py-2.5 lg:px-4 lg:py-3 rounded-lg text-slate-300 hover:bg-red-600 hover:text-white transition-all duration-200 w-full text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ClipboardDocumentListIcon_CogIcon_HomeIcon_ShoppingBagIcon_UsersIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-4 h-4 lg:w-5 lg:h-5 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm lg:text-base\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/Providers.tsx":
/*!************************************************!*\
  !*** ./src/components/providers/Providers.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"(ssr)/./src/context/AuthContext.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                richColors: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\providers\\\\Providers.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHcUQ7QUFDcEI7QUFNbEIsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQzVELHFCQUNFLDhEQUFDSCw4REFBWUE7O1lBQ1ZHOzBCQUNELDhEQUFDRiwyQ0FBT0E7Z0JBQUNHLFVBQVM7Z0JBQVlDLFVBQVU7Ozs7Ozs7Ozs7OztBQUc5QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBTEkgQ09NUFVURVJTXFxEZXNrdG9wXFxhZG1pblxcY3dhLWFkbWluXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dC9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnc29ubmVyJztcblxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogUHJvdmlkZXJzUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgICAgPFRvYXN0ZXIgcG9zaXRpb249XCJ0b3AtcmlnaHRcIiByaWNoQ29sb3JzIC8+XG4gICAgPC9BdXRoUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwiVG9hc3RlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicG9zaXRpb24iLCJyaWNoQ29sb3JzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/video-blogs/VideoBlogForm.tsx":
/*!******************************************************!*\
  !*** ./src/components/video-blogs/VideoBlogForm.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoBlogForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/videoBlogService */ \"(ssr)/./src/lib/api/videoBlogService.ts\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction VideoBlogForm({ videoBlog, onSubmit, onCancel, loading = false }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        videoUrl: '',\n        thumbnailUrl: '',\n        youtubeUrl: '',\n        youtubeVideoId: '',\n        category: 'General',\n        tags: [],\n        duration: '',\n        isActive: true\n    });\n    const [videoType, setVideoType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('direct');\n    const [tagInput, setTagInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [categories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'General',\n        'Technology',\n        'Education',\n        'Entertainment',\n        'Business',\n        'Health',\n        'Sports',\n        'Travel',\n        'Food',\n        'Lifestyle'\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoBlogForm.useEffect\": ()=>{\n            if (videoBlog) {\n                setFormData({\n                    title: videoBlog.title || '',\n                    description: videoBlog.description || '',\n                    videoUrl: videoBlog.videoUrl || '',\n                    thumbnailUrl: videoBlog.thumbnailUrl || '',\n                    youtubeUrl: videoBlog.youtubeUrl || '',\n                    youtubeVideoId: videoBlog.youtubeVideoId || '',\n                    category: videoBlog.category || 'General',\n                    tags: videoBlog.tags || [],\n                    duration: videoBlog.duration ? videoBlog.duration.toString() : '',\n                    isActive: videoBlog.isActive !== undefined ? videoBlog.isActive : true\n                });\n                // Determine video type based on existing data\n                if (videoBlog.youtubeVideoId || videoBlog.youtubeUrl) {\n                    setVideoType('youtube');\n                } else {\n                    setVideoType('direct');\n                }\n            }\n        }\n    }[\"VideoBlogForm.useEffect\"], [\n        videoBlog\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        if (type === 'checkbox') {\n            const checked = e.target.checked;\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: checked\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleVideoTypeChange = (type)=>{\n        setVideoType(type);\n        // Clear relevant fields when switching types\n        if (type === 'youtube') {\n            setFormData((prev)=>({\n                    ...prev,\n                    videoUrl: ''\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    youtubeUrl: '',\n                    youtubeVideoId: ''\n                }));\n        }\n    };\n    const handleYouTubeUrlChange = (e)=>{\n        const url = e.target.value;\n        setFormData((prev)=>({\n                ...prev,\n                youtubeUrl: url\n            }));\n        // Extract YouTube video ID\n        if (url) {\n            const videoId = _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].extractYouTubeVideoId(url);\n            if (videoId) {\n                setFormData((prev)=>({\n                        ...prev,\n                        youtubeVideoId: videoId,\n                        videoUrl: url,\n                        thumbnailUrl: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`\n                    }));\n            }\n        }\n    };\n    const handleAddTag = ()=>{\n        if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        tagInput.trim()\n                    ]\n                }));\n            setTagInput('');\n        }\n    };\n    const handleRemoveTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const handleTagInputKeyPress = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleAddTag();\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.title.trim()) {\n            alert('Title is required');\n            return;\n        }\n        if (!formData.description.trim()) {\n            alert('Description is required');\n            return;\n        }\n        if (videoType === 'youtube') {\n            if (!formData.youtubeUrl.trim()) {\n                alert('YouTube URL is required');\n                return;\n            }\n            if (!_lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isValidYouTubeUrl(formData.youtubeUrl)) {\n                alert('Please enter a valid YouTube URL');\n                return;\n            }\n        } else {\n            if (!formData.videoUrl.trim()) {\n                alert('Video URL is required');\n                return;\n            }\n        }\n        if (!formData.thumbnailUrl.trim()) {\n            alert('Thumbnail URL is required');\n            return;\n        }\n        // Prepare submission data\n        const submitData = {\n            title: formData.title.trim(),\n            description: formData.description.trim(),\n            videoUrl: videoType === 'youtube' ? formData.youtubeUrl : formData.videoUrl,\n            thumbnailUrl: formData.thumbnailUrl.trim(),\n            category: formData.category,\n            tags: formData.tags,\n            isActive: formData.isActive\n        };\n        // Add YouTube-specific fields\n        if (videoType === 'youtube') {\n            submitData.youtubeUrl = formData.youtubeUrl.trim();\n            submitData.youtubeVideoId = formData.youtubeVideoId.trim();\n        }\n        // Add duration if provided\n        if (formData.duration) {\n            const duration = parseInt(formData.duration);\n            if (!isNaN(duration) && duration > 0) {\n                submitData.duration = duration;\n            }\n        }\n        try {\n            await onSubmit(submitData);\n        } catch (error) {\n            console.error('Form submission error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Basic Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"title\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Title *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"title\",\n                                        name: \"title\",\n                                        value: formData.title,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        maxLength: 200,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter video blog title\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"description\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Description *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"description\",\n                                        name: \"description\",\n                                        value: formData.description,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        rows: 4,\n                                        maxLength: 1000,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter video blog description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"category\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"category\",\n                                        name: \"category\",\n                                        value: formData.category,\n                                        onChange: handleInputChange,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Video Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Video Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"radio\",\n                                                name: \"videoType\",\n                                                value: \"direct\",\n                                                checked: videoType === 'direct',\n                                                onChange: ()=>handleVideoTypeChange('direct'),\n                                                className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-gray-700\",\n                                                children: \"Direct URL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"radio\",\n                                                name: \"videoType\",\n                                                value: \"youtube\",\n                                                checked: videoType === 'youtube',\n                                                onChange: ()=>handleVideoTypeChange('youtube'),\n                                                className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-gray-700\",\n                                                children: \"YouTube\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-6\",\n                        children: [\n                            videoType === 'youtube' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"youtubeUrl\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"YouTube URL *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"youtubeUrl\",\n                                        name: \"youtubeUrl\",\n                                        value: formData.youtubeUrl,\n                                        onChange: handleYouTubeUrlChange,\n                                        required: videoType === 'youtube',\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"https://www.youtube.com/watch?v=...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.youtubeVideoId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-green-600\",\n                                        children: [\n                                            \"Video ID extracted: \",\n                                            formData.youtubeVideoId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"videoUrl\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Video URL *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"videoUrl\",\n                                        name: \"videoUrl\",\n                                        value: formData.videoUrl,\n                                        onChange: handleInputChange,\n                                        required: videoType === 'direct',\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"https://example.com/video.mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"thumbnailUrl\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Thumbnail URL *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"thumbnailUrl\",\n                                        name: \"thumbnailUrl\",\n                                        value: formData.thumbnailUrl,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"https://example.com/thumbnail.jpg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"duration\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Duration (seconds)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"duration\",\n                                        name: \"duration\",\n                                        value: formData.duration,\n                                        onChange: handleInputChange,\n                                        min: \"0\",\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"e.g., 300 for 5 minutes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Tags\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: tagInput,\n                                        onChange: (e)=>setTagInput(e.target.value),\n                                        onKeyPress: handleTagInputKeyPress,\n                                        className: \"flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter a tag and press Enter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleAddTag,\n                                        className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this),\n                            formData.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                        children: [\n                                            tag,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleRemoveTag(tag),\n                                                className: \"ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"isActive\",\n                                name: \"isActive\",\n                                checked: formData.isActive,\n                                onChange: handleInputChange,\n                                className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"isActive\",\n                                className: \"ml-2 block text-sm text-gray-900\",\n                                children: \"Active (visible to users)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: onCancel,\n                        className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                        disabled: loading,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                        disabled: loading,\n                        children: loading ? 'Saving...' : videoBlog ? 'Update Video Blog' : 'Create Video Blog'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/video-blogs/VideoBlogForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/api/apiClient */ \"(ssr)/./lib/api/apiClient.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst initialState = {\n    user: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null\n};\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialState);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        const userId = localStorage.getItem('userId');\n                        const name = localStorage.getItem('name');\n                        const email = localStorage.getItem('email');\n                        const phone = localStorage.getItem('phone');\n                        const isAdmin = localStorage.getItem('isAdmin');\n                        const createdAt = localStorage.getItem('createdAt');\n                        if (token && userId) {\n                            setAuthState({\n                                user: {\n                                    id: userId,\n                                    name: name || 'User',\n                                    email: email || '',\n                                    phone: phone || '',\n                                    isAdmin: isAdmin === 'true',\n                                    createdAt: createdAt || new Date().toISOString()\n                                },\n                                isAuthenticated: true,\n                                isLoading: false,\n                                error: null\n                            });\n                        } else {\n                            setAuthState({\n                                ...initialState,\n                                isLoading: false\n                            });\n                        }\n                    } catch (error) {\n                        console.log(error);\n                        setAuthState({\n                            ...initialState,\n                            isLoading: false\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await _lib_api_apiClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post('/auth/login', {\n                email,\n                password\n            });\n            console.log(\"Login response:\", response.data);\n            if (response.data.status === 'success') {\n                const { user, token } = response.data.data;\n                // Store auth data in localStorage\n                localStorage.setItem('token', token);\n                localStorage.setItem('userId', user.id);\n                localStorage.setItem('name', user.name);\n                localStorage.setItem('email', user.email);\n                localStorage.setItem('phone', user.phone || '');\n                localStorage.setItem('isAdmin', user.isAdmin ? 'true' : 'false');\n                localStorage.setItem('createdAt', user.createdAt || new Date().toISOString());\n                // Update auth state\n                setAuthState({\n                    user: {\n                        id: user.id,\n                        name: user.name,\n                        email: user.email,\n                        phone: user.phone || '',\n                        isAdmin: user.isAdmin,\n                        createdAt: user.createdAt || new Date().toISOString()\n                    },\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged in successfully!');\n                // Check if user is admin\n                // if (user.isAdmin) {\n                router.push('/dashboard');\n            // } else {\n            //   throw new Error('Access denied. Admin privileges required.');\n            // }\n            } else {\n                throw new Error('Login failed');\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            const errorMessage = error.response?.data?.message || 'Invalid email or password';\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n        }\n    };\n    // Logout function\n    const logout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('name');\n        localStorage.removeItem('email');\n        localStorage.removeItem('phone');\n        localStorage.removeItem('isAdmin');\n        localStorage.removeItem('createdAt');\n        // Reset auth state\n        setAuthState(initialState);\n        // Redirect to login\n        router.push('/auth/login');\n        sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('Logged out successfully!');\n    };\n    // Function to directly set auth state (used by auth-callback)\n    const setAuthStateDirectly = (state)=>{\n        setAuthState(state);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            logout,\n            setAuthState: setAuthStateDirectly\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/videoBlogService.ts":
/*!*****************************************!*\
  !*** ./src/lib/api/videoBlogService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nclass VideoBlogService {\n    /**\r\n   * Get all video blogs with optional filters\r\n   */ async getVideoBlogs(filters = {}) {\n        try {\n            const params = new URLSearchParams();\n            // Add filters to query params\n            if (filters.search) {\n                params.append('search', filters.search);\n            }\n            if (filters.category && filters.category !== 'all') {\n                params.append('category', filters.category);\n            }\n            if (filters.tag) {\n                params.append('tag', filters.tag);\n            }\n            if (filters.page) {\n                params.append('page', filters.page.toString());\n            }\n            if (filters.limit) {\n                params.append('limit', filters.limit.toString());\n            }\n            const queryString = params.toString();\n            const url = `${this.baseURL}/video-blogs${queryString ? `?${queryString}` : ''}`;\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(url, {\n                headers\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error fetching video blogs:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get a single video blog by ID\r\n   */ async getVideoBlog(id) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to fetch video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error fetching video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Create a new video blog\r\n   */ async createVideoBlog(data) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs`, {\n                method: 'POST',\n                headers,\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to create video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error creating video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Update a video blog\r\n   */ async updateVideoBlog(id, data) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {\n                method: 'PATCH',\n                headers,\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to update video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error updating video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Delete a video blog\r\n   */ async deleteVideoBlog(id) {\n        try {\n            const token =  false ? 0 : null;\n            const headers = {\n                'Content-Type': 'application/json'\n            };\n            if (token) {\n                headers['Authorization'] = `Bearer ${token}`;\n            }\n            const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {\n                method: 'DELETE',\n                headers\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        message: 'Failed to delete video blog'\n                    }));\n                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n            }\n            // Handle 204 No Content response\n            if (response.status === 204) {\n                return {\n                    status: 'success',\n                    message: 'Video blog deleted successfully'\n                };\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Error deleting video blog:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get video blog categories\r\n   */ async getCategories() {\n        try {\n            const response = await this.getVideoBlogs();\n            const categories = [\n                ...new Set(response.data.videoBlogs.map((blog)=>blog.category))\n            ];\n            return categories.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n            return [\n                'General',\n                'Technology',\n                'Education',\n                'Entertainment',\n                'Business',\n                'Health',\n                'Sports'\n            ];\n        }\n    }\n    /**\r\n   * Get all unique tags\r\n   */ async getTags() {\n        try {\n            const response = await this.getVideoBlogs();\n            const allTags = response.data.videoBlogs.flatMap((blog)=>blog.tags);\n            const uniqueTags = [\n                ...new Set(allTags)\n            ];\n            return uniqueTags.filter(Boolean);\n        } catch (error) {\n            console.error('Error fetching tags:', error);\n            return [];\n        }\n    }\n    /**\r\n   * Extract YouTube video ID from URL\r\n   */ extractYouTubeVideoId(url) {\n        const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n        const match = url.match(regex);\n        return match ? match[1] : null;\n    }\n    /**\r\n   * Validate YouTube URL\r\n   */ isValidYouTubeUrl(url) {\n        const regex = /^https?:\\/\\/(www\\.)?(youtube\\.com|youtu\\.be)\\/.+/;\n        return regex.test(url);\n    }\n    constructor(){\n        this.baseURL = '/api';\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new VideoBlogService());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/videoBlogService.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/sonner","vendor-chunks/@heroicons","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&page=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&appPaths=%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage&pagePath=private-next-app-dir%2Fvideo-blogs%2F%5Bid%5D%2Fedit%2Fpage.tsx&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();