/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/video-blogs/[id]/route";
exports.ids = ["app/api/video-blogs/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&page=%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&page=%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ALI_COMPUTERS_Desktop_admin_cwa_admin_src_app_api_video_blogs_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/video-blogs/[id]/route.ts */ \"(rsc)/./src/app/api/video-blogs/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/video-blogs/[id]/route\",\n        pathname: \"/api/video-blogs/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/video-blogs/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\api\\\\video-blogs\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ALI_COMPUTERS_Desktop_admin_cwa_admin_src_app_api_video_blogs_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ2aWRlby1ibG9ncyUyRiU1QmlkJTVEJTJGcm91dGUmcGFnZT0lMkZhcGklMkZ2aWRlby1ibG9ncyUyRiU1QmlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGdmlkZW8tYmxvZ3MlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNBTEklMjBDT01QVVRFUlMlNUNEZXNrdG9wJTVDYWRtaW4lNUNjd2EtYWRtaW4lNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q0FMSSUyMENPTVBVVEVSUyU1Q0Rlc2t0b3AlNUNhZG1pbiU1Q2N3YS1hZG1pbiZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDK0M7QUFDNUg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXEFMSSBDT01QVVRFUlNcXFxcRGVza3RvcFxcXFxhZG1pblxcXFxjd2EtYWRtaW5cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcdmlkZW8tYmxvZ3NcXFxcW2lkXVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvdmlkZW8tYmxvZ3MvW2lkXS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3ZpZGVvLWJsb2dzL1tpZF1cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3ZpZGVvLWJsb2dzL1tpZF0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxBTEkgQ09NUFVURVJTXFxcXERlc2t0b3BcXFxcYWRtaW5cXFxcY3dhLWFkbWluXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHZpZGVvLWJsb2dzXFxcXFtpZF1cXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&page=%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/video-blogs/[id]/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/video-blogs/[id]/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst BACKEND_URL = \"http://localhost:5002\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Authorization header required'\n            }, {\n                status: 401\n            });\n        }\n        const response = await fetch(`${BACKEND_URL}/api/video-blogs/${params.id}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': authHeader\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Backend error response:', errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Backend service error',\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            const responseText = await response.text();\n            console.error('Non-JSON response from backend:', responseText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Invalid response format from backend'\n            }, {\n                status: 502\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        console.error('Get video blog proxy error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, { params }) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Authorization header required'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const response = await fetch(`${BACKEND_URL}/api/video-blogs/${params.id}`, {\n            method: 'PATCH',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': authHeader\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Backend error response:', errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Backend service error',\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            const responseText = await response.text();\n            console.error('Non-JSON response from backend:', responseText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Invalid response format from backend'\n            }, {\n                status: 502\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        console.error('Update video blog proxy error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Authorization header required'\n            }, {\n                status: 401\n            });\n        }\n        const response = await fetch(`${BACKEND_URL}/api/video-blogs/${params.id}`, {\n            method: 'DELETE',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': authHeader\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Backend error response:', errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Backend service error',\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        // Handle 204 No Content response\n        if (response.status === 204) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'success',\n                message: 'Video blog deleted successfully'\n            }, {\n                status: 200\n            });\n        }\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            // If it's not JSON but successful, return success message\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'success',\n                message: 'Video blog deleted successfully'\n            }, {\n                status: 200\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        console.error('Delete video blog proxy error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/video-blogs/[id]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&page=%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();