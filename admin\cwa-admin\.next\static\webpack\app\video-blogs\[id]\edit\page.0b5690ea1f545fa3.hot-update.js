"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/video-blogs/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/video-blogs/VideoBlogForm.tsx":
/*!******************************************************!*\
  !*** ./src/components/video-blogs/VideoBlogForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoBlogForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/videoBlogService */ \"(app-pages-browser)/./src/lib/api/videoBlogService.ts\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction VideoBlogForm(param) {\n    let { videoBlog, onSubmit, onCancel, loading = false } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        videoUrl: '',\n        thumbnailUrl: '',\n        youtubeUrl: '',\n        youtubeVideoId: '',\n        category: 'General',\n        tags: [],\n        duration: '',\n        isActive: true\n    });\n    const [videoType, setVideoType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('direct');\n    const [tagInput, setTagInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [categories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'General',\n        'Technology',\n        'Education',\n        'Entertainment',\n        'Business',\n        'Health',\n        'Sports',\n        'Travel',\n        'Food',\n        'Lifestyle'\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoBlogForm.useEffect\": ()=>{\n            if (videoBlog) {\n                setFormData({\n                    title: videoBlog.title || '',\n                    description: videoBlog.description || '',\n                    videoUrl: videoBlog.videoUrl || '',\n                    thumbnailUrl: videoBlog.thumbnailUrl || '',\n                    youtubeUrl: videoBlog.youtubeUrl || '',\n                    youtubeVideoId: videoBlog.youtubeVideoId || '',\n                    category: videoBlog.category || 'General',\n                    tags: videoBlog.tags || [],\n                    duration: videoBlog.duration ? videoBlog.duration.toString() : '',\n                    isActive: videoBlog.isActive !== undefined ? videoBlog.isActive : true\n                });\n                // Determine video type based on existing data\n                if (videoBlog.youtubeVideoId || videoBlog.youtubeUrl) {\n                    setVideoType('youtube');\n                } else {\n                    setVideoType('direct');\n                }\n            }\n        }\n    }[\"VideoBlogForm.useEffect\"], [\n        videoBlog\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        if (type === 'checkbox') {\n            const checked = e.target.checked;\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: checked\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleVideoTypeChange = (type)=>{\n        setVideoType(type);\n        // Clear relevant fields when switching types\n        if (type === 'youtube') {\n            setFormData((prev)=>({\n                    ...prev,\n                    videoUrl: ''\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    youtubeUrl: '',\n                    youtubeVideoId: ''\n                }));\n        }\n    };\n    const handleYouTubeUrlChange = (e)=>{\n        const url = e.target.value;\n        setFormData((prev)=>({\n                ...prev,\n                youtubeUrl: url\n            }));\n        // Extract YouTube video ID\n        if (url) {\n            const videoId = _lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].extractYouTubeVideoId(url);\n            if (videoId) {\n                setFormData((prev)=>({\n                        ...prev,\n                        youtubeVideoId: videoId,\n                        videoUrl: url,\n                        thumbnailUrl: prev.thumbnailUrl || \"https://img.youtube.com/vi/\".concat(videoId, \"/maxresdefault.jpg\")\n                    }));\n            } else {\n                // Clear video ID if URL is invalid\n                setFormData((prev)=>({\n                        ...prev,\n                        youtubeVideoId: ''\n                    }));\n            }\n        } else {\n            // Clear fields if URL is empty\n            setFormData((prev)=>({\n                    ...prev,\n                    youtubeVideoId: '',\n                    videoUrl: ''\n                }));\n        }\n    };\n    const handleAddTag = ()=>{\n        if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags,\n                        tagInput.trim()\n                    ]\n                }));\n            setTagInput('');\n        }\n    };\n    const handleRemoveTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                tags: prev.tags.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    const handleTagInputKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleAddTag();\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!formData.title.trim()) {\n            alert('Title is required');\n            return;\n        }\n        if (!formData.description.trim()) {\n            alert('Description is required');\n            return;\n        }\n        if (videoType === 'youtube') {\n            if (!formData.youtubeUrl.trim()) {\n                alert('YouTube URL is required');\n                return;\n            }\n            if (!_lib_api_videoBlogService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isValidYouTubeUrl(formData.youtubeUrl)) {\n                alert('Please enter a valid YouTube URL');\n                return;\n            }\n            if (!formData.youtubeVideoId.trim()) {\n                alert('Could not extract YouTube video ID from the URL. Please check the URL format.');\n                return;\n            }\n        } else {\n            if (!formData.videoUrl.trim()) {\n                alert('Video URL is required');\n                return;\n            }\n        }\n        if (!formData.thumbnailUrl.trim()) {\n            alert('Thumbnail URL is required');\n            return;\n        }\n        // Prepare submission data\n        const submitData = {\n            title: formData.title.trim(),\n            description: formData.description.trim(),\n            videoUrl: videoType === 'youtube' ? formData.youtubeUrl : formData.videoUrl,\n            thumbnailUrl: formData.thumbnailUrl.trim(),\n            category: formData.category,\n            tags: formData.tags,\n            isActive: formData.isActive\n        };\n        // Add YouTube-specific fields\n        if (videoType === 'youtube') {\n            submitData.youtubeUrl = formData.youtubeUrl.trim();\n            submitData.youtubeVideoId = formData.youtubeVideoId.trim();\n        }\n        // Add duration if provided\n        if (formData.duration) {\n            const duration = parseInt(formData.duration);\n            if (!isNaN(duration) && duration > 0) {\n                submitData.duration = duration;\n            }\n        }\n        try {\n            await onSubmit(submitData);\n        } catch (error) {\n            console.error('Form submission error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Basic Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"title\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Title *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"title\",\n                                        name: \"title\",\n                                        value: formData.title,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        maxLength: 200,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter video blog title\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"description\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Description *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"description\",\n                                        name: \"description\",\n                                        value: formData.description,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        rows: 4,\n                                        maxLength: 1000,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter video blog description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"category\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Category\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"category\",\n                                        name: \"category\",\n                                        value: formData.category,\n                                        onChange: handleInputChange,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category,\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Video Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Video Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"radio\",\n                                                name: \"videoType\",\n                                                value: \"direct\",\n                                                checked: videoType === 'direct',\n                                                onChange: ()=>handleVideoTypeChange('direct'),\n                                                className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-gray-700\",\n                                                children: \"Direct URL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"radio\",\n                                                name: \"videoType\",\n                                                value: \"youtube\",\n                                                checked: videoType === 'youtube',\n                                                onChange: ()=>handleVideoTypeChange('youtube'),\n                                                className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-gray-700\",\n                                                children: \"YouTube\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-6\",\n                        children: [\n                            videoType === 'youtube' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"youtubeUrl\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"YouTube URL *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"youtubeUrl\",\n                                        name: \"youtubeUrl\",\n                                        value: formData.youtubeUrl,\n                                        onChange: handleYouTubeUrlChange,\n                                        required: videoType === 'youtube',\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"https://www.youtube.com/watch?v=...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.youtubeVideoId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-green-600\",\n                                        children: [\n                                            \"Video ID extracted: \",\n                                            formData.youtubeVideoId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"videoUrl\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Video URL *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"videoUrl\",\n                                        name: \"videoUrl\",\n                                        value: formData.videoUrl,\n                                        onChange: handleInputChange,\n                                        required: videoType === 'direct',\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"https://example.com/video.mp4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"thumbnailUrl\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Thumbnail URL *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"url\",\n                                        id: \"thumbnailUrl\",\n                                        name: \"thumbnailUrl\",\n                                        value: formData.thumbnailUrl,\n                                        onChange: handleInputChange,\n                                        required: true,\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"https://example.com/thumbnail.jpg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"duration\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Duration (seconds)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"duration\",\n                                        name: \"duration\",\n                                        value: formData.duration,\n                                        onChange: handleInputChange,\n                                        min: \"0\",\n                                        className: \"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"e.g., 300 for 5 minutes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Tags\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: tagInput,\n                                        onChange: (e)=>setTagInput(e.target.value),\n                                        onKeyDown: handleTagInputKeyDown,\n                                        className: \"flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                        placeholder: \"Enter a tag and press Enter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleAddTag,\n                                        className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            formData.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: formData.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                        children: [\n                                            tag,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleRemoveTag(tag),\n                                                className: \"ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"isActive\",\n                                name: \"isActive\",\n                                checked: formData.isActive,\n                                onChange: handleInputChange,\n                                className: \"focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"isActive\",\n                                className: \"ml-2 block text-sm text-gray-900\",\n                                children: \"Active (visible to users)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: onCancel,\n                        className: \"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                        disabled: loading,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                        disabled: loading,\n                        children: loading ? 'Saving...' : videoBlog ? 'Update Video Blog' : 'Create Video Blog'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\components\\\\video-blogs\\\\VideoBlogForm.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoBlogForm, \"EoOjDxGxz9Q6MtA6oU/tdfSGQYU=\");\n_c = VideoBlogForm;\nvar _c;\n$RefreshReg$(_c, \"VideoBlogForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/video-blogs/VideoBlogForm.tsx\n"));

/***/ })

});