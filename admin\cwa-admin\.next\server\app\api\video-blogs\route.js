/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/video-blogs/route";
exports.ids = ["app/api/video-blogs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2Froute&page=%2Fapi%2Fvideo-blogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2Froute&page=%2Fapi%2Fvideo-blogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ALI_COMPUTERS_Desktop_admin_cwa_admin_src_app_api_video_blogs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/video-blogs/route.ts */ \"(rsc)/./src/app/api/video-blogs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/video-blogs/route\",\n        pathname: \"/api/video-blogs\",\n        filename: \"route\",\n        bundlePath: \"app/api/video-blogs/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\admin\\\\cwa-admin\\\\src\\\\app\\\\api\\\\video-blogs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ALI_COMPUTERS_Desktop_admin_cwa_admin_src_app_api_video_blogs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2Froute&page=%2Fapi%2Fvideo-blogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/video-blogs/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/video-blogs/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst BACKEND_URL = \"http://localhost:5002\" || 0;\nasync function GET(request) {\n    try {\n        // Get the authorization header from the request\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Authorization header required'\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters for filtering, sorting, pagination\n        const { searchParams } = new URL(request.url);\n        const queryString = searchParams.toString();\n        const url = `${BACKEND_URL}/api/video-blogs${queryString ? `?${queryString}` : ''}`;\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': authHeader\n            }\n        });\n        // Check if response is ok and has JSON content\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Backend error response:', errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Backend service error',\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        // Check if response is JSON\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            const responseText = await response.text();\n            console.error('Non-JSON response from backend:', responseText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Invalid response format from backend'\n            }, {\n                status: 502\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        console.error('Video blogs proxy error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Authorization header required'\n            }, {\n                status: 401\n            });\n        }\n        // Get the JSON data from the request (video blogs use JSON, not FormData)\n        const body = await request.json();\n        // Forward the JSON data to the backend\n        const response = await fetch(`${BACKEND_URL}/api/video-blogs`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': authHeader\n            },\n            body: JSON.stringify(body)\n        });\n        // Check if response is ok and has JSON content\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Backend error response:', errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Backend service error',\n                details: errorText\n            }, {\n                status: response.status\n            });\n        }\n        // Check if response is JSON\n        const contentType = response.headers.get('content-type');\n        if (!contentType || !contentType.includes('application/json')) {\n            const responseText = await response.text();\n            console.error('Non-JSON response from backend:', responseText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                status: 'error',\n                message: 'Invalid response format from backend'\n            }, {\n                status: 502\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        console.error('Create video blog proxy error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: 'error',\n            message: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/video-blogs/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvideo-blogs%2Froute&page=%2Fapi%2Fvideo-blogs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvideo-blogs%2Froute.ts&appDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CALI%20COMPUTERS%5CDesktop%5Cadmin%5Ccwa-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();